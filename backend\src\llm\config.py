"""
LLM配置管理
"""

import os
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional


class ModelProvider(Enum):
    """模型提供商枚举"""

    OPENROUTER = "openrouter"
    OPENAI = "openai"


class TaskType(Enum):
    """任务类型枚举"""

    CHARACTER_ANALYSIS = "character_analysis"
    DIALOGUE_ANNOTATION = "dialogue_annotation"


@dataclass
class ModelConfig:
    """单个模型配置

    字段说明：
    - max_output_tokens: 单次调用的最大输出token数（对应API的max_tokens参数）
    - context_length: 模型支持的最大上下文窗口长度（输入+输出的总和）
    - input_cost_per_1k_tokens: 输入token的成本（人民币/千tokens）
    - output_cost_per_1k_tokens: 输出token的成本（人民币/千tokens）
    """

    name: str
    provider: ModelProvider
    api_key_env: str
    base_url: Optional[str] = None
    input_cost_per_1k_tokens: float = 0.0  # 输入token成本（¥/千tokens）
    output_cost_per_1k_tokens: float = 0.0  # 输出token成本（¥/千tokens）
    max_output_tokens: int = 4000  # 最大输出token数(对应API的max_tokens参数)
    context_length: int = 128000  # 模型最大上下文窗口长度(输入+输出总和)
    temperature: float = 0.1
    timeout: int = 30
    retry_count: int = 2

    # @property
    # def cost_per_1k_tokens(self) -> float:
    #     """兼容性属性：返回输出token成本（向后兼容）"""
    #     return self.output_cost_per_1k_tokens

    @property
    def max_tokens(self) -> int:
        """兼容性属性：返回最大输出token数（向后兼容）"""
        return self.max_output_tokens

    def calculate_cost(self, input_tokens: int, output_tokens: int) -> float:
        """计算总成本"""
        input_cost = (input_tokens / 1000) * self.input_cost_per_1k_tokens
        output_cost = (output_tokens / 1000) * self.output_cost_per_1k_tokens
        return input_cost + output_cost


@dataclass
class TaskConfig:
    """任务配置"""

    primary_model: str
    fallback_models: List[str]
    budget_limit: float
    max_retries: int = 3
    quality_threshold: float = 0.8


class LLMConfig:
    """LLM配置管理器"""

    def __init__(self):
        self.models = self._init_models()
        self.tasks = self._init_tasks()
        self.cost_limits = self._init_cost_limits()

    def _init_models(self) -> Dict[str, ModelConfig]:
        """初始化模型配置"""
        return {
            # OpenRouter模型
            "claude-sonnet-4": ModelConfig(
                name="anthropic/claude-sonnet-4",
                provider=ModelProvider.OPENROUTER,
                api_key_env="OPENROUTER_API_KEY",
                base_url="https://openrouter.ai/api/v1",
                input_cost_per_1k_tokens=0.0216,
                output_cost_per_1k_tokens=0.108,
                max_output_tokens=64_000,
                context_length=200_000,
                temperature=0.1,
            ),
            "deepseek-v3-free": ModelConfig(
                name="openrouter/deepseek/deepseek-chat-v3-0324:free",  # 使用openrouter前缀
                provider=ModelProvider.OPENROUTER,
                api_key_env="OPENROUTER_API_KEY",
                base_url="https://openrouter.ai/api/v1",
                input_cost_per_1k_tokens=0,
                output_cost_per_1k_tokens=0,
                max_output_tokens=128_000,
                context_length=128_000,
                temperature=0.1,
            ),
            "deepseek-v3": ModelConfig(
                name="openrouter/deepseek/deepseek-chat-v3-0324",  # 使用openrouter前缀
                provider=ModelProvider.OPENROUTER,
                api_key_env="OPENROUTER_API_KEY",
                base_url="https://openrouter.ai/api/v1",
                input_cost_per_1k_tokens=0.00216,  # $0.30/M tokens = ¥0.00216/千tokens
                output_cost_per_1k_tokens=0.006336,  # $0.88/M tokens = ¥0.006336/千tokens
                max_output_tokens=164_000,  # 164K max output
                context_length=164_000,  # 164K context window
                temperature=0.1,
            ),
            "gemini-2.5-pro": ModelConfig(
                name="google/gemini-2.5-pro",  # 正确的OpenRouter模型名称
                provider=ModelProvider.OPENROUTER,
                api_key_env="OPENROUTER_API_KEY",
                base_url="https://openrouter.ai/api/v1",
                input_cost_per_1k_tokens=0.009,  # $1.25/M tokens = ¥0.009/千tokens
                output_cost_per_1k_tokens=0.072,  # $10/M tokens = ¥0.072/千tokens
                max_output_tokens=66_000,  # 66K max output
                context_length=1_048_576,  # 1M+ context window
                temperature=0.1,
            ),
            "gemini-2.5-flash": ModelConfig(
                name="google/gemini-2.5-flash",  # 正确的OpenRouter模型名称
                provider=ModelProvider.OPENROUTER,
                api_key_env="OPENROUTER_API_KEY",
                base_url="https://openrouter.ai/api/v1",
                input_cost_per_1k_tokens=0.00108,  # $0.15/M tokens = ¥0.00108/千tokens
                output_cost_per_1k_tokens=0.00432,  # $0.60/M tokens = ¥0.00432/千tokens
                max_output_tokens=66_000,  # 66K max output
                context_length=1_048_576,  # 1M+ context window
                temperature=0.1,
            ),
            "gemini-2.5-flash-lite": ModelConfig(
                name="google/gemini-2.5-flash-lite-preview-06-17",
                provider=ModelProvider.OPENROUTER,
                api_key_env="OPENROUTER_API_KEY",
                base_url="https://openrouter.ai/api/v1",
                input_cost_per_1k_tokens=0.00072,  # $0.10/M tokens = ¥0.00072/千tokens
                output_cost_per_1k_tokens=0.00288,  # $0.40/M tokens = ¥0.00288/千tokens
                max_output_tokens=66_000,  # 66K max output
                context_length=1_048_576,  # 1.05M context window
                temperature=0.1,
            ),
            "qwen3-235b-free": ModelConfig(
                name="openrouter/qwen/qwen3-235b-a22b:free",  # 使用openrouter前缀
                provider=ModelProvider.OPENROUTER,
                api_key_env="OPENROUTER_API_KEY",
                base_url="https://openrouter.ai/api/v1",
                input_cost_per_1k_tokens=0,
                output_cost_per_1k_tokens=0,
                max_output_tokens=40_960,
                context_length=40_960,
                temperature=0.1,
            ),
            "qwen3-235b": ModelConfig(
                name="openrouter/qwen/qwen3-235b-a22b",  # 使用openrouter前缀
                provider=ModelProvider.OPENROUTER,
                api_key_env="OPENROUTER_API_KEY",
                base_url="https://openrouter.ai/api/v1",
                input_cost_per_1k_tokens=0.000936,  # $0.13/M tokens = ¥0.000936/千tokens
                output_cost_per_1k_tokens=0.00432,  # $0.60/M tokens = ¥0.00432/千tokens
                max_output_tokens=41_000,  # 41K max output
                context_length=41_000,  # 41K context window
                temperature=0.1,
            ),

            # Google AI Studio (Gemini) 模型
            "google-gemini-2.5-pro": ModelConfig(
                name="gemini/gemini-2.5-pro",
                provider=ModelProvider.OPENAI,  # 使用Google AI Studio
                api_key_env="GEMINI_API_KEY",
                base_url=None,  # Google AI Studio不需要base_url
                input_cost_per_1k_tokens=0.0,  # 免费模型
                output_cost_per_1k_tokens=0.0,  # 免费模型
                max_output_tokens=8192,
                context_length=2_097_152,  # 2M context
                temperature=0.1,
            ),
            "google-gemini-2.5-flash": ModelConfig(
                name="gemini/gemini-2.5-flash",
                provider=ModelProvider.OPENAI,
                api_key_env="GEMINI_API_KEY",
                base_url=None,  # Google AI Studio不需要base_url
                input_cost_per_1k_tokens=0.0,  # 免费模型
                output_cost_per_1k_tokens=0.0,  # 免费模型
                max_output_tokens=8192,
                context_length=1_048_576,  # 1M context
                temperature=0.1,
            ),
            "google-gemini-2.5-flash-lite": ModelConfig(
                name="gemini/gemini-2.5-flash-lite-preview-06-17",
                provider=ModelProvider.OPENAI,
                api_key_env="GEMINI_API_KEY",
                base_url=None,  # Google AI Studio不需要base_url
                input_cost_per_1k_tokens=0.0,  # 免费模型
                output_cost_per_1k_tokens=0.0,  # 免费模型
                max_output_tokens=66_000,  # 66K max output
                context_length=1_048_576,  # 1M context
                temperature=0.1,
            )

        }

    def _init_tasks(self) -> Dict[TaskType, TaskConfig]:
        """初始化任务配置"""
        # 通用任务配置，适用于所有AI处理任务
        default_task_config = TaskConfig(
            primary_model= "google-gemini-2.5-flash",
            fallback_models=[
                "gemini-2.5-flash",
                "qwen3-235b",
                "deepseek-v3-free", 
                "qwen3-235b-free", 
                "google-gemini-2.5-flash-lite",
                "google-gemini-2.5-pro",
                "gemini-2.5-flash-lite",
                "gemini-2.5-pro",
                "deepseek-v3"
            ],
            budget_limit=10.0,
            max_retries=3,
            quality_threshold=0.8,
        )

        return {
            TaskType.CHARACTER_ANALYSIS: default_task_config,
            TaskType.DIALOGUE_ANNOTATION: default_task_config,
        }

    def _init_cost_limits(self) -> Dict[str, float]:
        """初始化成本限制"""
        return {
            "daily_budget": float(os.getenv("LLM_DAILY_BUDGET", "100.0")),
            "per_chapter_limit": float(os.getenv("LLM_CHAPTER_LIMIT", "5.0")),
            "emergency_threshold": float(os.getenv("LLM_EMERGENCY_THRESHOLD", "0.1")),
        }

    def get_model_config(self, model_name: str) -> Optional[ModelConfig]:
        """获取模型配置"""
        return self.models.get(model_name)

    def get_task_config(self, task_type: TaskType) -> Optional[TaskConfig]:
        """获取任务配置"""
        return self.tasks.get(task_type)

    def get_models_by_provider(self, provider: ModelProvider) -> List[str]:
        """根据提供商获取模型列表"""
        return [
            name for name, config in self.models.items() if config.provider == provider
        ]

    def validate_config(self) -> bool:
        """验证配置有效性"""
        # 检查必要的环境变量
        required_env_vars = set()
        for config in self.models.values():
            required_env_vars.add(config.api_key_env)

        missing_vars = []
        for var in required_env_vars:
            if not os.getenv(var):
                missing_vars.append(var)

        if missing_vars:
            print(f"警告: 缺少环境变量: {missing_vars}")
            return False

        return True
